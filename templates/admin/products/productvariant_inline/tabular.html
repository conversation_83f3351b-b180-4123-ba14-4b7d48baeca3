{% load i18n admin_urls static admin_modify %}
<div class="js-inline-admin-formset inline-group" id="{{ inline_admin_formset.formset.prefix }}-group"
     data-inline-type="tabular"
     data-inline-formset="{{ inline_admin_formset.inline_formset_data }}">
  <div class="tabular inline-related {% if forloop.last %}last-related{% endif %}">
    {{ inline_admin_formset.formset.management_form }}
    <fieldset class="module {{ inline_admin_formset.classes }}">
      <h2>{{ inline_admin_formset.opts.verbose_name_plural|capfirst }}</h2>
      {{ inline_admin_formset.formset.non_form_errors }}
      <table>
        <thead>
        <tr>
          <th class="original"></th>
          {% for field in inline_admin_formset.fields %}
            {% if not field.widget.is_hidden %}
              <th{% if field.required %} class="required"{% endif %}{% if field.name == 'drag_handle' %} class="field-drag_handle"{% endif %}>
                {% if field.name == 'drag_handle' %}
                  ⋮⋮
                {% else %}
                  {{ field.label|capfirst }}
                  {% if field.help_text %}
                    <div class="help">{{ field.help_text|safe }}</div>
                  {% endif %}
                {% endif %}
              </th>
            {% endif %}
          {% endfor %}
        </tr>
        </thead>
        <tbody>
        {% for inline_admin_form in inline_admin_formset %}
          {% if inline_admin_form.form.non_field_errors %}
            <tr>
              <td colspan="{{ inline_admin_formset.fields|length }}">{{ inline_admin_form.form.non_field_errors }}</td>
            </tr>
          {% endif %}
          <tr class="form-row {% if inline_admin_form.original or inline_admin_form.show_url %}has_original{% endif %}{% if forloop.last and inline_admin_formset.has_add_permission %} empty-form{% endif %}"
              id="{{ inline_admin_formset.formset.prefix }}-{% if not forloop.last %}{{ forloop.counter0 }}{% else %}empty{% endif %}">
            <td class="original">
              {% if inline_admin_form.original or inline_admin_form.show_url %}
                <p>
                  {% if inline_admin_form.original %}
                    {{ inline_admin_form.original }}
                    {% if inline_admin_form.model_admin.show_change_link and inline_admin_form.model_admin.has_registered_model and inline_admin_form.original.pk %}
                      <a href="{% url inline_admin_form.model_admin.opts|admin_urlname:'change' inline_admin_form.original.pk|admin_urlquote %}"
                         class="{% if inline_admin_formset.has_change_permission %}inlinechangelink{% else %}inlineviewlink{% endif %}">
                        {% if inline_admin_formset.has_change_permission %}{% trans "Change" %}{% else %}{% trans "View" %}{% endif %}
                      </a>
                    {% endif %}
                  {% endif %}
                  {% if inline_admin_form.show_url %}
                    <a href="{{ inline_admin_form.absolute_url }}">{% trans "View on site" %}</a>
                  {% endif %}
                </p>
              {% endif %}
              {% if inline_admin_form.needs_explicit_pk_field %}
                {{ inline_admin_form.pk_field.field }}
              {% endif %}
              {% if inline_admin_form.fk_field %}
                {{ inline_admin_form.fk_field.field }}
              {% endif %}
              {% spaceless %}
                {% for fieldset in inline_admin_form %}
                  {% for line in fieldset %}
                    {% for field in line %}
                      {% if field.field.is_hidden %}
                        {{ field.field }}
                      {% endif %}
                    {% endfor %}
                  {% endfor %}
                {% endfor %}
              {% endspaceless %}
            </td>

            {% for fieldset in inline_admin_form %}
              {% for line in fieldset %}
                {% for field in line %}
                  {% if not field.field.is_hidden %}
                    <td{% if field.field.name %} class="field-{{ field.field.name }}"{% endif %}>
                      {% if field.field.name == 'drag_handle' %}
                        <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
                      {% elif field.field.name == 'order' %}
                        <!-- Custom order field handling -->
                        <div class="order-field-container">
                          {{ field.field.errors.as_ul }}
                          {{ field.field }}
                        </div>
                      {% elif field.is_readonly %}
                        <p>{{ field.contents }}</p>
                      {% else %}
                        {{ field.field.errors.as_ul }}
                        {{ field.field }}
                      {% endif %}
                    </td>
                  {% endif %}
                {% endfor %}
              {% endfor %}
            {% endfor %}
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </fieldset>
  </div>
</div>

<style>
  /* Hide the default delete checkbox column */
  .delete-checkbox-column,
  .field-DELETE,
  td.delete,
  th.delete-header,
  input[name$="-DELETE"],
  label[for$="-DELETE"] {
    display: none !important;
  }
</style>

<script type="text/javascript">
(function($) {
  // Helper function to update order fields after sorting
  function updateProductVariantOrderFields($tbody) {
    console.log("Updating ProductVariant order fields...");
    $tbody.find('tr:not(.empty-form)').each(function (index) {
      var $row = $(this);
      var newOrder = index + 1;

      // Find the order field input (should be visible now)
      var $orderField = $row.find('input[name$="-order"], .order-input');
      if ($orderField.length > 0) {
        $orderField.val(newOrder);
        console.log("Updated order field input to:", newOrder, "for row", index);

        // Add visual feedback to show the field was updated
        $orderField.addClass('order-updated');
        setTimeout(function() {
          $orderField.removeClass('order-updated');
        }, 500);
      } else {
        console.log("No order field found for row", index);
      }
    });
  }

  $(document).ready(function() {
    console.log("ProductVariant inline template loaded with drag-and-drop support");

    // Hide the default delete checkboxes and related elements
    $('.field-DELETE').parent().addClass('delete-checkbox-column').hide();
    $('.field-DELETE').hide();
    $('td.delete').hide();

    // Add a class to any header containing "Delete" text and hide it
    $('th').each(function() {
      if ($(this).text().trim() === 'Delete') {
        $(this).addClass('delete-header').hide();
      }
    });

    // Hide any delete-related inputs
    $('input[name$="-DELETE"]').hide();
    $('label[for$="-DELETE"]').hide();

    // Wait for the sortable to be initialized, then override the updateOrderFields function
    setTimeout(function() {
      // Find all tabular inlines with drag handles
      $('.inline-group .tabular').each(function() {
        var $tabular = $(this);
        var $tbody = $tabular.find('tbody');

        if ($tbody.find('.drag-handle').length > 0 && typeof Sortable !== 'undefined') {
          console.log("Found ProductVariant inline with drag handles");

          // Initialize our custom sortable for ProductVariant
          if (!$tabular.hasClass('sortable-initialized')) {
            $tabular.addClass('sortable-initialized sortable-tabular-inline');

            Sortable.create($tbody[0], {
              handle: '.drag-handle',
              animation: 150,
              ghostClass: 'drag-over',
              onEnd: function (evt) {
                console.log("Drag ended, updating order fields");
                updateProductVariantOrderFields($tbody);

                // Add visual feedback
                var $row = $($tbody[0].children[evt.newIndex]);
                $row.addClass('sortable-success');
                setTimeout(function () {
                  $row.removeClass('sortable-success');
                }, 1000);
              },
              filter: '.empty-form',
              draggable: 'tr:not(.empty-form)'
            });

            // Initial order update
            updateProductVariantOrderFields($tbody);
          }
        }
      });
    }, 600);

    console.log("Drag-and-drop initialization complete for ProductVariant inline");
  });

  // Handle dynamic form addition (when "Add another" is clicked)
  $(document).on('formset:added', function (event, $row) {
    var $inlineGroup = $row.closest('.inline-group');
    var $tbody = $inlineGroup.find('tbody');
    if ($inlineGroup.find('.drag-handle').length > 0) {
      setTimeout(function() {
        updateProductVariantOrderFields($tbody);
      }, 100);
    }
  });

  // Handle form deletion
  $(document).on('formset:removed', function (event, $row) {
    var $inlineGroup = $row.closest('.inline-group');
    var $tbody = $inlineGroup.find('tbody');
    if ($inlineGroup.find('.drag-handle').length > 0) {
      setTimeout(function () {
        updateProductVariantOrderFields($tbody);
      }, 100);
    }
  });

  // Ensure order fields are updated before form submission
  $(document).on('submit', 'form', function() {
    $('.sortable-tabular-inline tbody').each(function() {
      updateProductVariantOrderFields($(this));
    });
    console.log("Order fields updated before form submission");
  });

})(django.jQuery);
</script>
