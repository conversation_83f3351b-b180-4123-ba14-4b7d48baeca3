from django.contrib import admin
from django.urls import reverse
from django.utils.safestring import mark_safe
from django import forms
from mptt.admin import DraggableMPTTAdmin
from ordered_model.admin import OrderedTabularInline, OrderedModelAdmin
from .forms import ProductVariantForm, ProductTypeForm, AttributeForm, CategoryForm, ProductImageForm
from .models import (Category, Product, ProductVariant, ProductImage, AttributeValue, Attribute, ProductTypeAttribute,
                     ProductType, Review, ProductVariantAttributeValue,
                     Brand, BrandProductType, Discount)


class EditLinkInline(object):
    def edit(self, instance):
        url = reverse(
            f'admin:{instance._meta.app_label}_{instance._meta.model_name}_change',
            args=[instance.pk]
        )
        if instance.pk:
            link = mark_safe('<a href="{u}">edit</a>'.format(u=url))
            return link
        else:
            return ''


@admin.register(Category)
class CategoryAdmin(DraggableMPTTAdmin):
    form = CategoryForm
    mptt_indent_field = 'title'
    list_display = ['id', 'tree_actions', 'indented_title', 'related_products_count',
                    'related_products_cumulative_count', 'slug', 'is_active']
    search_fields = ['title']
    list_display_links = ['indented_title']
    autocomplete_fields = ['parent']

    def get_queryset(self, request):
        qs = super().get_queryset(request)

        # Add cumulative product count
        qs = Category.objects.add_related_count(
            qs,
            Product,
            'category',
            'products_cumulative_count',
            cumulative=True)

        # Add non cumulative product count
        qs = Category.objects.add_related_count(qs,
                                                Product,
                                                'category',
                                                'products_count',
                                                cumulative=False)
        return qs

    def related_products_count(self, instance):
        return instance.products_count

    related_products_count.short_description = 'Related products (for this specific category)'

    def related_products_cumulative_count(self, instance):
        return instance.products_cumulative_count

    related_products_cumulative_count.short_description = 'Related products (in tree)'


class ProductImageInline(admin.TabularInline):
    list_display = ['alternative_text', 'image', 'order']
    readonly_fields = ['order']
    model = ProductImage
    form = ProductImageForm
    extra = 1

    # Fields to display in the inline
    # Include product_variant but we'll hide it with CSS
    fields = ['alternative_text', 'image', 'product_variant', 'order']

    # Hide the default delete checkbox
    can_delete = False

    # Add a template to customize the inline
    template = 'admin/products/productvariant/edit_inline/tabular.html'

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        # Set the product_variant automatically when adding a new image
        if obj and 'product_variant' in formset.form.base_fields:
            formset.form.base_fields['product_variant'].initial = obj
            # Make it a hidden field
            formset.form.base_fields['product_variant'].widget = forms.HiddenInput()
        return formset


# class ProductVariantInline(EditLinkInline, admin.TabularInline):
#     form = ProductVariantForm
#     model = ProductVariant
#     autocomplete_fields = ['price_label']
#     readonly_fields = ['edit', 'order']
#     min_num = 1  # Minimum number of product variant instances
#     extra = 1

class ProductVariantInline(EditLinkInline, OrderedTabularInline):
    model = ProductVariant
    form = ProductVariantForm
    fields = ('drag_handle', 'sku', 'price', 'stock_qty', 'condition', 'price_label', 'order', 'edit')
    readonly_fields = ('drag_handle', 'order', 'edit')
    ordering = ('order',)
    autocomplete_fields = ['price_label']
    extra = 1
    template = 'admin/products/productvariant_inline/tabular.html'

    def drag_handle(self, obj):
        """Provide drag handle for reordering"""
        return '⋮⋮'
    drag_handle.short_description = '⋮⋮'

    class Media:
        css = {
            'all': ('admin/css/sortable-tabular-inline.css',)
        }
        js = (
            'admin/js/vendor/sortable.min.js',
            'admin/js/sortable-tabular-inline.js',
        )


class AttributeInline(admin.TabularInline):
    model = Attribute.product_type_attribute.through


class AttributeValueInline(admin.TabularInline):
    model = AttributeValue.product_variant_attribute_value.through
    autocomplete_fields = ['attribute_value']
    template = 'admin/products/attributevalue/edit_inline/tabular.html'
    extra = 1


class AttributeValueProductInline(admin.TabularInline):
    model = AttributeValue.product_attr_value.through


class ReviewInline(admin.StackedInline):
    model = Review
    extra = 0


"""
When you include foreign key fields in list_display,
Django tries to access these related objects for each row in the admin list view.
If there are a large number of Review objects, or if the related Product
or Customer queries are slow, this can cause significant performance issues
or even timeout errors, resulting in an empty table.
"""


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'get_product', 'get_customer', 'rating', 'posted_at']
    list_filter = ['product', 'posted_at']
    search_fields = ['title', 'description']
    '''
    Instead of directly referencing the foreign key fields,
    you can create methods that return the string representation of the related objects.
    '''

    def get_product(self, obj):
        return obj.product.title

    def get_customer(self, obj):
        return f"{obj.customer.user.email}"

    get_product.short_description = 'Product'
    get_customer.short_description = 'Customer'


@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'is_active']
    ordering = ['id', 'title', 'is_active']
    search_fields = ['title']
    list_filter = ['is_active']
    prepopulated_fields = {
        'slug': ['title']
    }


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'is_digital', 'is_active', 'product_type', 'category',
                    'updated_at', 'created_at']
    ordering = ['id', 'title', 'is_digital', 'is_active', 'product_type', 'category',
                'updated_at', 'created_at']
    list_filter = ['product_type', 'category']
    search_fields = ['title', 'category']
    autocomplete_fields = ['product_type', 'category']
    readonly_fields = ['average_rating']
    prepopulated_fields = {
        'slug': ['title']
    }
    inlines = [
        ProductVariantInline,
        # AttributeValueProductInline,
        ReviewInline,
    ]

    class Media:
        css = {
            'all': (
                'admin/css/sortable-tabular-inline.css',
                'css/admin_custom.css',
            )
        }
        js = (
            'admin/js/vendor/sortable.min.js',
            'admin/js/sortable-tabular-inline.js',
        )


@admin.register(ProductVariant)
class ProductVariantAdmin(admin.ModelAdmin):
    form = ProductVariantForm
    list_display = ['id', 'price_label', 'product', 'price', 'sku', 'stock_qty', 'is_active',
                    'weight', 'condition', 'created_at', 'updated_at']
    ordering = ['order']
    list_filter = ['is_active', 'stock_qty']
    search_fields = ['product__title', 'price_label']
    autocomplete_fields = ['product', 'price_label']
    readonly_fields = ['order']
    inlines = [
        ProductImageInline,
        AttributeValueInline,
    ]

    # Use our custom template for the change form
    change_form_template = 'admin/products/productvariant/change_form.html'

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if obj is None:  # This is a creation form
            form.base_fields['price_label'].widget.attrs['disabled'] = 'disabled'
        return form

    def save_model(self, request, obj, form, change):
        if not change:  # This is a new instance
            obj.price_label = None  # Ensure price_label is not set for new instances
        super().save_model(request, obj, form, change)

    def save_formset(self, request, form, formset, change):
        """Custom handling for inline formsets"""
        instances = formset.save(commit=False)

        # Handle new inline objects
        for instance in instances:
            # If this is a ProductImage instance and product_variant is not set
            if isinstance(instance, ProductImage) and not instance.product_variant_id:
                # Set the product_variant to the parent object
                instance.product_variant = form.instance
            instance.save()

        # Handle deleted inline objects
        for obj in formset.deleted_objects:
            obj.delete()

        formset.save_m2m()


@admin.register(ProductVariantAttributeValue)
class ProductVariantAttributeValueAdmin(admin.ModelAdmin):
    list_display = ['id', 'attribute_value', 'product_variant', 'is_active']
    ordering = ['id', 'attribute_value', 'product_variant', 'is_active']
    list_filter = ['is_active']
    search_fields = ['attribute_value', 'product_variant']
    autocomplete_fields = ['attribute_value', 'product_variant']
    extra = 1


@admin.register(Attribute)
class AttributeAdmin(admin.ModelAdmin):
    form = AttributeForm
    list_display = ['id', 'title']
    search_fields = ['id', 'title']
    list_filter = ['product_type_attribute']


@admin.register(ProductTypeAttribute)
class ProductTypeAttributeAdmin(admin.ModelAdmin):
    list_display = ['id', 'product_type', 'attribute']
    ordering = ['id', 'product_type', 'attribute']
    search_fields = ['product_type', 'attribute']
    list_filter = ['product_type', 'attribute']
    autocomplete_fields = ['product_type', 'attribute']


class ProductTypeFilter(admin.SimpleListFilter):
    title = 'Product Type'
    parameter_name = 'product_type'

    def lookups(self, request, model_admin):
        product_types = ProductType.objects.all()
        return [(pt.id, pt.title) for pt in product_types]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(attribute__product_type_attribute__product_type__id=self.value()).distinct()
        return queryset


@admin.register(AttributeValue)
class AttributeValueAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'attribute', 'get_product_type', 'is_active']
    ordering = ['id', 'attribute__title', 'attribute', 'is_active']
    search_fields = ['attribute_value', 'attribute__title']
    list_filter = [ProductTypeFilter, 'attribute__title', 'is_active']

    # list_select_related = ['attribute']
    autocomplete_fields = ['attribute']

    def get_product_type(self, obj):
        product_types = ProductType.objects.filter(attribute=obj.attribute).distinct()
        return ", ".join([pt.title for pt in product_types])

    get_product_type.short_description = "Product Type"


@admin.register(ProductType)
class ProductTypeAdmin(admin.ModelAdmin):
    form = ProductTypeForm
    list_display = ['id', 'title', 'parent']
    inlines = [
        AttributeInline
    ]
    search_fields = ['title']
    readonly_fields = ['parent']

    class Media:
        css = {
            'all': ('admin/css/widgets.css', 'css/admin_custom.css',)
        }


@admin.register(BrandProductType)
class BrandProductTypeAdmin(admin.ModelAdmin):
    list_display = ['id', 'brand', 'product_type']
    ordering = ['id', 'brand', 'product_type']
    search_fields = ['brand', 'product_type']
    list_filter = ['brand', 'product_type']
    autocomplete_fields = ['brand', 'product_type']


# @admin.register(Review)
# class ReviewAdmin(admin.ModelAdmin):
#     list_display = ['product', 'name', 'description', 'rating', 'posted_at']
#     list_filter = ['product', 'posted_at']
#     search_fields = ['name', 'description']

# Define the safely_delete_image function before using it
def safely_delete_image(modeladmin, request, queryset):
    for image in queryset:
        image.delete()


safely_delete_image.short_description = "Safely delete selected images"


# Register ProductImage directly with admin
@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    form = ProductImageForm
    list_display = ['id', 'alternative_text', 'get_product_variant', 'order']
    list_filter = ['product_variant__product__title']
    search_fields = ['alternative_text', 'product_variant__product__title']
    readonly_fields = ['order']
    actions = [safely_delete_image]

    def get_product_variant(self, obj):
        return f"{obj.product_variant.product.title} - {obj.product_variant.sku}"

    get_product_variant.short_description = 'Product Variant'


@admin.register(Discount)
class DiscountAdmin(admin.ModelAdmin):
    list_display = (
        'name',
        'discount_percentage',
        'start_date',
        'end_date',
        'is_active',
        'get_product_variants',
        'is_valid_status',
    )
    list_filter = ('is_active', 'start_date', 'end_date')  # Filters for quick access
    search_fields = ('name',)  # Allow search by discount name
    ordering = ('-start_date',)  # Order discounts by start date, newest first
    filter_horizontal = ('product_variants',)  # For ManyToManyField UI

    def get_product_variants(self, obj):
        """Display linked product variants."""
        return ", ".join([pv.sku for pv in obj.product_variants.all()])

    get_product_variants.short_description = 'Linked Product Variants'

    def is_valid_status(self, obj):
        """Show if the discount is currently valid."""
        return obj.is_valid()

    is_valid_status.boolean = True
    is_valid_status.short_description = 'Currently Valid'
