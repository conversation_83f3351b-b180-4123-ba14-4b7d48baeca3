from django.shortcuts import get_object_or_404
from rest_framework.exceptions import PermissionDenied, ValidationError
from django.db import IntegrityError
from django.db.models import ProtectedError
from django.core.exceptions import ValidationError as DjangoValidationError, PermissionDenied
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from utils.permissions import IsAdminOrOwner, IsAdminOrCustomer
from .models import Customer, Address
from .serializers import DetailedCustomerSerializer, CustomerUpdateSerializer, AddressSerializer


class CustomerViewSet(ModelViewSet):
    queryset = Customer.objects.prefetch_related('address').all()
    http_method_names = ['get', 'patch', 'head', 'options']

    # permission_classes = [IsAdminOrOwner]

    def get_serializer_class(self):
        # Choose serializer based on action.
        if self.action == 'partial_update':
            return CustomerUpdateSerializer
        return DetailedCustomerSerializer

    def get_queryset(self):
        # Base queryset with prefetching
        base_queryset = Customer.objects.select_related('user').prefetch_related('address')

        if self.action == 'list':
            if not (self.request.user.is_staff or self.request.user.is_superuser):
                raise PermissionDenied("You do not have permission to list all customers.")
            return base_queryset

        # For non-list actions
        if self.request.user.is_staff or self.request.user.is_superuser:
            return base_queryset
        return base_queryset.filter(user=self.request.user)

    @action(detail=False, methods=['get', 'patch'])
    def me(self, request):
        # Retrieve or partially update the authenticated customer's profile.
        if not request.user.is_authenticated:
            return Response({"detail": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            customer = get_object_or_404(Customer, user=request.user)

            if request.method == 'GET':
                serializer = self.get_serializer(customer)
                return Response(serializer.data)

            # PATCH request to update customer
            # Add elif request.method == 'PATCH':   for better readability
            serializer = CustomerUpdateSerializer(customer, data=request.data, partial=True)
            try:
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(serializer.data)
            except (ValidationError, DjangoValidationError) as e:
                return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        try:
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return Response(serializer.data)
        except (ValidationError, DjangoValidationError) as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def perform_update(self, serializer):
        # Override perform_update to handle DjangoValidationError.
        try:
            serializer.save()
        except DjangoValidationError as e:
            raise ValidationError(detail=str(e))


class AddressViewSet(ModelViewSet):
    queryset = Address.objects.all()
    serializer_class = AddressSerializer
    permission_classes = [IsAdminOrCustomer]

    def get_queryset(self):
        user = self.request.user
        if not user.is_authenticated:
            return Address.objects.none()  # Return empty queryset for unauthenticated users

        try:
            if user.is_staff or user.is_superuser:
                return Address.objects.all()
            return Address.objects.filter(customer=user.customer)
        except AttributeError:
            # If user doesn't have a customer profile, return empty queryset
            return Address.objects.none()

    def perform_create(self, serializer):
        user = self.request.user
        if not user.is_authenticated:
            raise ValidationError("User must be authenticated to create an address.")
        try:
            serializer.save(customer=user.customer)
        except IntegrityError as e:
            raise ValidationError(f"Database error: {str(e)}")

    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        try:
            return super().update(request, *args, **kwargs)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        try:
            return super().destroy(request, *args, **kwargs)
        except PermissionDenied as e:
            return Response({"error": str(e)}, status=status.HTTP_403_FORBIDDEN)
        except ProtectedError:
            return Response({"error": "This address is linked to existing orders and cannot be deleted."},
                            status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as e:
            return Response({"error": f"Database error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)
