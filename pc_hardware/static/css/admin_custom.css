/* Custom Admin CSS */

/* General admin customizations */
.admin-custom {
    /* Add any general admin customizations here */
}

/* Drag and drop enhancements for product variants */
.sortable-tabular-inline .drag-handle {
    cursor: move;
    color: #666;
    font-size: 18px;
    padding: 8px 5px;
    text-align: center;
    width: 30px;
    user-select: none;
    font-weight: bold;
    letter-spacing: -2px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-block;
    min-height: 20px;
    line-height: 1.2;
}

.sortable-tabular-inline .drag-handle:hover {
    color: #333;
    background-color: #e9ecef;
    border-radius: 4px;
    transform: scale(1.1);
}

/* Enhanced visual feedback for dragging */
.sortable-tabular-inline tbody tr.drag-over {
    background-color: #fff3cd !important;
    border-top: 3px solid #ffc107 !important;
    border-bottom: 3px solid #ffc107 !important;
}

.sortable-tabular-inline tbody tr.sortable-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    transition: background-color 0.3s ease;
}

/* Dragging state */
.sortable-tabular-inline tbody tr.sortable-ghost {
    opacity: 0.5;
    background-color: #e3f2fd !important;
}

/* Order field styling */
.field-order input {
    width: 60px;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-weight: bold;
    color: #495057;
}

.field-order p {
    text-align: center;
    font-weight: bold;
    color: #495057;
    margin: 0;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* Order field container styling */
.order-field-container {
    position: relative;
    text-align: center;
}

.order-field-container input[name$="-order"] {
    display: none; /* Hide the actual input field */
}

.order-display {
    display: inline-block;
    text-align: center;
    font-weight: bold;
    color: #495057;
    margin: 0;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 3px;
    border: 1px solid #dee2e6;
    min-width: 40px;
    transition: all 0.2s ease;
}

.sortable-tabular-inline tbody tr:hover .order-display {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

/* Drag handle column */
.field-drag_handle {
    width: 50px;
    padding: 0 !important;
    text-align: center;
}

.field-drag_handle th {
    width: 50px;
    padding: 8px 4px !important;
    text-align: center;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: bold;
    color: #666;
}

.field-drag_handle td {
    text-align: center;
    vertical-align: middle;
    padding: 4px !important;
}

/* Improve table row hover effects */
.sortable-tabular-inline tbody tr:hover {
    background-color: #f8f9fa;
}

.sortable-tabular-inline tbody tr:hover .drag-handle {
    color: #333;
    background-color: #dee2e6;
}
